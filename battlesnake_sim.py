import random
import time
import contextlib
import io
import threading
import os
import importlib.util  # For dynamic loading
import sys             # For dynamic loading
from concurrent.futures import ThreadPoolExecutor, as_completed
import multiprocessing

# --- Configuration ---
BOARD_WIDTH = 11
BOARD_HEIGHT = 11
TURN_DELAY_SECONDS = 0.01 # Set to 0 for instant simulation, or >0 for pauses

# --- <<< --- <<< --- <<< --- <<< --- <<< --- <<< --- <<< --- <<< --- ---
# --- DEFINE YOUR BATTLESNAKE AI FILES HERE ---
# --- <<< --- <<< --- <<< --- <<< --- <<< --- <<< --- <<< --- <<< --- ---
SNAKE_FILES_TO_LOAD = [
    "wurmchen.py",  # Make sure this file exists!
    "wurmchen.py",
]
# --- >>> --- >>> --- >>> --- >>> --- >>> --- >>> --- >>> --- >>> --- ---

# --- AI Loading Function ---
ai_load_counter = 0
def load_ai_from_file(filepath, heuristic_values=None, silent=False):
    global ai_load_counter
    if not os.path.exists(filepath):
        if not silent: print(f"❌ Error: File not found - {filepath}")
        return None
    base_name = os.path.splitext(os.path.basename(filepath))[0]
    module_name = f"{base_name}_{ai_load_counter}"
    ai_load_counter += 1
    spec = importlib.util.spec_from_file_location(module_name, filepath)
    if spec and spec.loader:
        module = importlib.util.module_from_spec(spec)
        sys.modules[module_name] = module
        try:
            # Suppress prints during loading
            with contextlib.redirect_stdout(io.StringIO()), contextlib.redirect_stderr(io.StringIO()):
                spec.loader.exec_module(module)
            if not silent: print(f"✅ Successfully loaded AI from {filepath} as {module_name}")
            if hasattr(module, 'info') and hasattr(module, 'move'):
                if not hasattr(module, 'start'): module.start = lambda gs: None
                if not hasattr(module, 'end'): module.end = lambda gs: None

                # If heuristic values are provided, override the module's heuristic values
                if heuristic_values is not None and hasattr(module, 'heuristikValues'):
                    module.heuristikValues = heuristic_values.copy()

                # Set silent mode if available
                if hasattr(module, 'silent_mode'):
                    module.silent_mode = silent

                return module
            else:
                if not silent: print(f"❌ Error: {filepath} missing 'info' or 'move'.")
                return None
        except Exception as e:
            if not silent: print(f"❌ Error executing {filepath}: {e}")
            if module_name in sys.modules: del sys.modules[module_name]
            return None
    else:
        if not silent: print(f"❌ Error: Could not load {filepath}")
        return None

# --- Simulation Core Classes (Point, Snake) ---
class Point:
    def __init__(self, x, y): self.x, self.y = x, y
    def __eq__(self, o): return isinstance(o, Point) and self.x == o.x and self.y == o.y
    def __hash__(self): return hash((self.x, self.y))
    def __repr__(self): return f"P({self.x},{self.y})"
    def to_dict(self): return {"x": self.x, "y": self.y}
    def moved(self, m):
        if m=="up": return Point(self.x, self.y+1)
        if m=="down": return Point(self.x, self.y-1)
        if m=="left": return Point(self.x-1, self.y)
        if m=="right": return Point(self.x+1, self.y)
        return self

class Snake:
    def __init__(self, id, name, body_points, ai_module):
        self.id, self.name, self.body = id, name, list(body_points)
        self.health, self.ai, self.is_alive = 100, ai_module, True
        self.last_move_made = "up"
        try: self.info_data = self.ai.info()
        except Exception: self.info_data = {"author": name}

    @property
    def head(self): return self.body[0]
    def to_dict(self): return {"id":self.id, "name":self.name, "health":self.health, "body":[p.to_dict() for p in self.body], "head":self.head.to_dict(), "length":len(self.body), "latency":"0", "shout":""}

# --- Simulation Class ---
class BattlesnakeSimulation:
    def __init__(self, width, height, snake_ais, silent=False):
        self.width, self.height, self.snake_ais = width, height, snake_ais
        self.turn, self.snakes, self.food = 0, [], []
        self.game_id = f"sim-{random.randint(1000, 9999)}"
        self.silent = silent
        self._setup_game()

    def _setup_game(self):
        start_positions = self._get_start_positions(len(self.snake_ais))
        for i, ai_module in enumerate(self.snake_ais):
            info = {}
            # Suppress info() prints
            with contextlib.redirect_stdout(io.StringIO()), contextlib.redirect_stderr(io.StringIO()):
                try: info = ai_module.info()
                except Exception as e: print(f"⚠️ Warning: info() for AI {i}: {e}")
            author = info.get('author', f'Unknown_{i}')
            snake_id = f"{author}-{i}"
            start_pos = start_positions[i]
            snake = Snake(snake_id, author, [start_pos]*3, ai_module)
            self.snakes.append(snake)
            gs = self._build_game_state(snake.id)
            # Suppress start() prints
            with contextlib.redirect_stdout(io.StringIO()), contextlib.redirect_stderr(io.StringIO()):
                try: snake.ai.start(gs)
                except Exception as e: print(f"Error calling start for {snake.name}: {e}")
        self._spawn_food(count=min(len(self.snakes) + 1, 5))

    def _get_start_positions(self, num_snakes):
        w, h = self.width - 1, self.height - 1; margin = 2
        corners = [Point(margin, margin), Point(w-margin, h-margin), Point(margin, h-margin), Point(w-margin, margin)]
        mid_points = [Point(w//2, margin), Point(w//2, h-margin), Point(margin, h//2), Point(w-margin, h//2)]
        all_pos = corners + mid_points; random.shuffle(all_pos)
        return all_pos[:num_snakes] if num_snakes <= len(all_pos) else [Point(random.randint(1,w-1), random.randint(1,h-1)) for _ in range(num_snakes)]

    def _spawn_food(self, count=1):
        occupied = {p for s in self.snakes if s.is_alive for p in s.body} | set(self.food)
        for _ in range(count):
            for _ in range(100):
                x, y = random.randint(0, self.width-1), random.randint(0, self.height-1)
                nf = Point(x, y)
                if nf not in occupied: self.food.append(nf); occupied.add(nf); break

    def _build_game_state(self, current_snake_id):
        return {"game":{"id":self.game_id}, "turn":self.turn, "board":{"height":self.height, "width":self.width, "snakes":[s.to_dict() for s in self.snakes if s.is_alive], "food":[f.to_dict() for f in self.food], "hazards":[]}, "you":next(s.to_dict() for s in self.snakes if s.id==current_snake_id)}

    def _check_collisions(self):
        dead = set(); living = [s for s in self.snakes if s.is_alive]
        for s in living:
            h = s.head
            if not (0 <= h.x < self.width and 0 <= h.y < self.height):
                if not self.silent: print(f"💀 Wall: {s.name}")
                dead.add(s.id); continue
            if h in list(s.body)[1:]:
                if not self.silent: print(f"💀 Self: {s.name}")
                dead.add(s.id); continue
            for o in living:
                if s.id == o.id: continue
                if h == o.head:
                    if not self.silent: print(f"💥 Head-to-Head: {s.name} vs {o.name}")
                    if len(s.body) <= len(o.body): dead.add(s.id)
                    if len(o.body) <= len(s.body): dead.add(o.id)
                elif h in set(o.body):
                    if not self.silent: print(f"💀 Body: {s.name} hit {o.name}")
                    dead.add(s.id); continue
        return dead

    def run_turn(self):
        self.turn += 1; living = [s for s in self.snakes if s.is_alive]
        if not living: return False # No one left, stop
        # print(f"\n--- Turn {self.turn} ---") # Console print less needed now

        moves = {}
        for s in living:
            gs = self._build_game_state(s.id)
            try:
                # Suppress move() prints
                with contextlib.redirect_stdout(io.StringIO()), contextlib.redirect_stderr(io.StringIO()):
                    moves[s.id] = s.ai.move(gs)["move"]
            except Exception as e: print(f"⚠️ Move Error ({s.name}): {e}."); moves[s.id] = s.last_move_made

        ate = {}
        for s in living: s.body.insert(0, s.head.moved(moves[s.id])); s.last_move_made = moves[s.id]; s.health -= 1; ate[s.id] = False

        new_food = []
        for f in self.food:
            eaters = [s for s in living if s.head == f]
            if eaters:
                winner = max(eaters, key=lambda s: len(s.body))
                winner.health = 100; ate[winner.id] = True
            else: new_food.append(f)
        self.food = new_food

        for s in living:
            if not ate[s.id]: s.body.pop()

        dead = set();
        for s in living:
            if s.health <= 0:
                if not self.silent: print(f"💀 Starve: {s.name}")
                dead.add(s.id)
        dead.update(self._check_collisions())

        for s_id in dead:
            s = next((s for s in self.snakes if s.id == s_id), None)
            if s and s.is_alive:
                s.is_alive = False; gs = self._build_game_state(s.id)
                with contextlib.redirect_stdout(io.StringIO()), contextlib.redirect_stderr(io.StringIO()):
                    try: s.ai.end(gs)
                    except Exception as e:
                        if not self.silent: print(f"Error calling end for {s.name}: {e}")
                if not self.silent: print(f"Eliminated: {s.name}")

        if random.random() < 0.15 or not self.food: self._spawn_food()
        if not self.silent: self.print_board()

        living_now = [s for s in self.snakes if s.is_alive]
        if len(living_now) == 1:
            if not self.silent: print(f"\n--- 🏆 GAME OVER --- {living_now[0].name} wins! --- 🏆")
            return False
        if not living_now:
            if not self.silent: print("\n--- 🏁 GAME OVER --- Draw! --- 🏁")
            return False
        return True

    def print_board(self):
        board = [['.' for _ in range(self.width)] for _ in range(self.height)]
        food_char = '🍎'

        for f in self.food: board[self.height - 1 - f.y][f.x] = food_char
        for i, snake in enumerate(self.snakes):
            if snake.is_alive:
                char = str(i % 10);
                head_char = chr(ord('A') + i)
                for j, p in enumerate(snake.body):
                    if 0 <= p.x < self.width and 0 <= p.y < self.height:
                        board[self.height - 1 - p.y][p.x] = f"\033[9{i + 1}m{head_char if j == 0 else char}\033[0m"

        cw = 3;
        sep = "-" * cw;
        print("+" + sep * self.width + "+")
        for y, row in enumerate(board):
            parts = [food_char + ' ' if c == food_char else ' . ' if c == '.' else ' ' + c + ' ' for c in row]
            print(f"|{''.join(parts)}| {self.height - 1 - y:<2}")
        print("+" + sep * self.width + "+")
        print(" " + ''.join([f"{x:^3}" for x in range(self.width)]) + " ")
        print("\n--- Status ---")
        for i, s in enumerate(self.snakes):
            if s.is_alive: print(
                f"\033[9{i + 1}m{chr(ord('A') + i)}{s.name[0]}\033[0m: {s.name} (L:{len(s.body)}, H:{s.health})")

def simulate(board_width, board_height, snake_ais, silent=False):
    if not silent:
        print(f"\n▶️ Starting simulation with {len(snake_ais)} snake(s)...")
    simulation = BattlesnakeSimulation(
        width=board_width,
        height=board_height,
        snake_ais=snake_ais,
        silent=silent
    )

    # Store original TURN_DELAY_SECONDS and set to 0 for silent mode
    global TURN_DELAY_SECONDS
    original_delay = TURN_DELAY_SECONDS
    if silent:
        TURN_DELAY_SECONDS = 0

    try:
        while simulation.run_turn():
            time.sleep(TURN_DELAY_SECONDS)

    except KeyboardInterrupt:
        if not silent:
            print("\nSimulation interrupted by user.")
    finally:
        if not silent:
            print("\nSimulation finished.")
        # Restore original delay
        TURN_DELAY_SECONDS = original_delay

        # Return the winner information
        living_snakes = [s for s in simulation.snakes if s.is_alive]
        if len(living_snakes) == 1:
            return living_snakes[0]  # Return the actual snake object
        else:
            return "Draw"

# --- Evolutionary Testing System ---
def mutate_heuristic(heuristic_values, mutation_rate=0.3, mutation_strength=0.2):
    """Create a mutated version of the heuristic values"""
    mutated = heuristic_values.copy()
    mutations_applied = 0

    for key in mutated:
        if random.random() < mutation_rate:
            # Apply random mutation
            current_value = mutated[key]
            mutation = random.uniform(-mutation_strength, mutation_strength)
            new_value = int(current_value * (1 + mutation))

            # Ensure the value actually changes
            if new_value != current_value:
                mutated[key] = new_value
                mutations_applied += 1

    # If no mutations were applied, force at least one
    if mutations_applied == 0:
        key = random.choice(list(mutated.keys()))
        current_value = mutated[key]
        mutation = random.uniform(-mutation_strength, mutation_strength)
        mutated[key] = int(current_value * (1 + mutation))

        # If still the same, add/subtract a small amount
        if mutated[key] == current_value:
            mutated[key] = current_value + random.choice([-1, 1])

    return mutated

def run_game_pair(mutated_heuristic, baseline_heuristic, game_id):
    """Run a pair of games (one with each starting order) and return results"""
    try:
        # Game 1: baseline first, mutated second
        baseline_ai_1 = load_ai_from_file("wurmchen.py", heuristic_values=baseline_heuristic, silent=True)
        mutated_ai_1 = load_ai_from_file("wurmchen.py", heuristic_values=mutated_heuristic, silent=True)

        if not baseline_ai_1 or not mutated_ai_1:
            return None

        winner_1 = simulate(BOARD_WIDTH, BOARD_HEIGHT, [baseline_ai_1, mutated_ai_1], silent=True)

        # Game 2: mutated first, baseline second
        mutated_ai_2 = load_ai_from_file("wurmchen.py", heuristic_values=mutated_heuristic, silent=True)
        baseline_ai_2 = load_ai_from_file("wurmchen.py", heuristic_values=baseline_heuristic, silent=True)

        if not mutated_ai_2 or not baseline_ai_2:
            return None

        winner_2 = simulate(BOARD_WIDTH, BOARD_HEIGHT, [mutated_ai_2, baseline_ai_2], silent=True)

        # Count results for this game pair
        wins_mutated = 0
        wins_baseline = 0
        draws = 0

        # Game 1: baseline first, mutated second
        if winner_1 == "Draw":
            draws += 0.5
        elif hasattr(winner_1, 'ai') and hasattr(winner_1.ai, 'heuristikValues'):
            if winner_1.ai.heuristikValues == mutated_heuristic:
                wins_mutated += 0.5
            else:
                wins_baseline += 0.5
        else:
            draws += 0.5

        # Game 2: mutated first, baseline second
        if winner_2 == "Draw":
            draws += 0.5
        elif hasattr(winner_2, 'ai') and hasattr(winner_2.ai, 'heuristikValues'):
            if winner_2.ai.heuristikValues == mutated_heuristic:
                wins_mutated += 0.5
            else:
                wins_baseline += 0.5
        else:
            draws += 0.5

        return {
            'game_id': game_id,
            'wins_mutated': wins_mutated,
            'wins_baseline': wins_baseline,
            'draws': draws
        }

    except Exception as e:
        print(f"❌ Error in game pair {game_id}: {e}")
        return None

def test_heuristic_vs_baseline_sequential(mutated_heuristic, baseline_heuristic, num_games=20):
    """Test a mutated heuristic against a baseline heuristic sequentially (fallback method)"""
    print(f"Testing mutated heuristic vs baseline over {num_games} games (sequential)...")

    wins_mutated = 0
    wins_baseline = 0
    draws = 0

    for game in range(num_games):
        result = run_game_pair(mutated_heuristic, baseline_heuristic, game)
        if result is not None:
            wins_mutated += result['wins_mutated']
            wins_baseline += result['wins_baseline']
            draws += result['draws']

            # Progress indicator
            if num_games <= 10 or (game + 1) % max(1, num_games // 4) == 0:
                print(f"  Completed {game + 1}/{num_games} games")

    win_rate_mutated = wins_mutated / num_games
    print(f"Results: Mutated {wins_mutated}, Baseline {wins_baseline}, Draws {draws}")
    print(f"Mutated win rate: {win_rate_mutated:.2%}")

    return {
        'win_rate': win_rate_mutated,
        'wins_mutated': wins_mutated,
        'wins_baseline': wins_baseline,
        'draws': draws
    }

def test_heuristic_vs_baseline(mutated_heuristic, baseline_heuristic, num_games=20, max_workers=None, use_threading=True):
    """Test a mutated heuristic against a baseline heuristic for num_games"""

    # For small numbers of games or if threading is disabled, use sequential
    if not use_threading or num_games <= 4:
        return test_heuristic_vs_baseline_sequential(mutated_heuristic, baseline_heuristic, num_games)

    if max_workers is None:
        # Use conservative threading to avoid issues
        max_workers = min(4, num_games)

    print(f"Testing mutated heuristic vs baseline over {num_games} games using {max_workers} threads...")

    wins_mutated = 0
    wins_baseline = 0
    draws = 0
    completed_games = 0

    try:
        # Use ThreadPoolExecutor to run games in parallel
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all game pairs
            futures = []
            for game_id in range(num_games):
                future = executor.submit(run_game_pair, mutated_heuristic, baseline_heuristic, game_id)
                futures.append((future, game_id))

            # Collect results as they complete
            for future, game_id in futures:
                try:
                    result = future.result(timeout=30)  # Shorter timeout
                    if result is not None:
                        wins_mutated += result['wins_mutated']
                        wins_baseline += result['wins_baseline']
                        draws += result['draws']
                        completed_games += 1

                        # Progress indicator - show every game for small numbers
                        if num_games <= 10 or completed_games % max(1, num_games // 4) == 0:
                            print(f"  Completed {completed_games}/{num_games} games")
                    else:
                        print(f"❌ Game pair {game_id} failed")

                except Exception as e:
                    print(f"❌ Exception in game pair {game_id}: {e}")

    except Exception as e:
        print(f"❌ ThreadPoolExecutor error: {e}")
        print("Falling back to sequential execution...")
        return test_heuristic_vs_baseline_sequential(mutated_heuristic, baseline_heuristic, num_games)

    if completed_games == 0:
        print("❌ No games completed successfully, falling back to sequential...")
        return test_heuristic_vs_baseline_sequential(mutated_heuristic, baseline_heuristic, num_games)

    win_rate_mutated = wins_mutated / completed_games
    print(f"Results: Mutated {wins_mutated}, Baseline {wins_baseline}, Draws {draws}")
    print(f"Mutated win rate: {win_rate_mutated:.2%}")

    return {
        'win_rate': win_rate_mutated,
        'wins_mutated': wins_mutated,
        'wins_baseline': wins_baseline,
        'draws': draws
    }

def evolutionary_heuristic_optimization(generations=10, num_games_per_test=12, max_workers=None, use_threading=False):
    """Run evolutionary optimization on heuristic values"""
    # Get default heuristic values from wurmchen.py
    default_ai = load_ai_from_file("wurmchen.py")
    if not default_ai:
        print("❌ Failed to load wurmchen.py")
        return

    current_best = default_ai.heuristikValues.copy()
    current_best_score = 0.5  # Baseline score (50% win rate against itself)

    if max_workers is None:
        max_workers = min(4, num_games_per_test)

    print("🧬 Starting Evolutionary Heuristic Optimization")
    print("=" * 60)
    if use_threading:
        print(f"Using {max_workers} threads for parallel game execution")
    else:
        print("Using sequential execution for stability")
    print(f"Testing with {num_games_per_test} games per generation")
    print("Default heuristic values:")
    for key, value in current_best.items():
        print(f"  {key}: {value}")
    print("=" * 60)

    for generation in range(generations):
        print(f"\n🧬 Generation {generation + 1}/{generations}")

        # Create mutated version
        mutated_heuristic = mutate_heuristic(current_best)

        print("Mutated heuristic values:")
        mutations_found = False
        for key, value in mutated_heuristic.items():
            if value != current_best[key]:
                print(f"  {key}: {current_best[key]} → {value}")
                mutations_found = True

        if not mutations_found:
            print("  No mutations in this generation")
            continue  # Skip testing if no mutations occurred

        # Test the mutated heuristic
        results = test_heuristic_vs_baseline(mutated_heuristic, current_best, num_games_per_test, max_workers, use_threading)

        if results is None:
            print("❌ Testing failed, skipping generation")
            continue

        # Check if this is better than current best
        if results['win_rate'] > current_best_score:
            print(f"🎉 Improvement found! Win rate: {results['win_rate']:.2%} (was {current_best_score:.2%})")
            current_best = mutated_heuristic.copy()
            current_best_score = results['win_rate']

            print("New best heuristic values:")
            for key, value in current_best.items():
                print(f"  {key}: {value}")
        else:
            print(f"❌ No improvement. Win rate: {results['win_rate']:.2%} (current best: {current_best_score:.2%})")

    print("\n" + "=" * 60)
    print("🏆 FINAL RESULTS")
    print("=" * 60)
    print(f"Best win rate achieved: {current_best_score:.2%}")
    print("Best heuristic values:")
    for key, value in current_best.items():
        print(f"  {key}: {value}")

    return current_best

# --- Main Execution ---
if __name__ == "__main__":
    print("Setting up Battlesnake Simulation...")

    # Check if user wants to run evolutionary optimization
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "--evolve":
        evolutionary_heuristic_optimization()
    else:
        # Load AI modules
        print("\nLoading AI Modules...")
        snake_modules_loaded = []
        for item in SNAKE_FILES_TO_LOAD:
            ai_module = load_ai_from_file(item)
            if ai_module:
                snake_modules_loaded.append(ai_module)

        if len(snake_modules_loaded) < 1:
            print("\n❌ Error: No AIs loaded. Cannot start simulation.")
        else:
            thread = threading.Thread(target=simulate, args=(BOARD_WIDTH, BOARD_HEIGHT, snake_modules_loaded))
            thread.start()
