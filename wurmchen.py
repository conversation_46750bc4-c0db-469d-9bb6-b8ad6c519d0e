import typing
from tree import build_tree, print_path, find_best_path, print_tree
import random

heuristikValues = {
    "UP_BORDER": -3,
    "DOWN_BORDER": -3,
    "LEFT_BORDER": -3,
    "RIGHT_BORDER": -3,
    "BLOCKED_POS": -1000,
    "FOOD": 100,
    "OPPONENT_ORTHO": -150,
    "OPPONENT_DIA": -50,
    "NEAR_DEATH": -100,
    "LIMITED_MOVEMENT": -30,
    "RISKY": -10,
}

# Global variable to control print output
silent_mode = False
def info() -> typing.Dict:
    # print("INFO")

    return {
        "apiversion": "1",
        "author": "Würmchen",
        "color": "#FFFFFF",
        "head": "smart-caterpillar",
        "tail": "rbc-necktie",
    }


# start is called when your Battlesnake begins a game
def start(game_state: typing.Dict):
    print("GAME START")



# end is called when your Battlesnake finishes a game
def end(game_state: typing.Dict):
    print("GAME OVER\n")


def get_heuristik(game_state):
    board_width = game_state['board']['width']
    board_height = game_state['board']['height']
    opponents = game_state['board']['snakes']
    food = game_state['board']['food']
    you = game_state["you"]
    my_id = you["id"]

    # Initialisiere das Grid mit Nullen
    board_values = [[0 for _ in range(board_width)] for _ in range(board_height)]

    # Felder am Speilfeldrand bieten weniger Ausweichmöglichkeiten -> leicht bestrafen
    for x in range(board_width):
        board_values[0][x] += heuristikValues["UP_BORDER"]  # obere Kante
        board_values[board_height - 1][x] += heuristikValues["DOWN_BORDER"]  # untere Kante
    for y in range(board_height):
        board_values[y][0] += heuristikValues["LEFT_BORDER"]  # linke Kante
        board_values[y][board_width - 1] += heuristikValues["RIGHT_BORDER"]  # rechte Kante

    # Blockierte Positionen mit extrem negativem Wert versehen
    blocked_positions = get_blocked_positions(game_state)
    for pos in blocked_positions:
        board_values[pos[1]][pos[0]] = heuristikValues["BLOCKED_POS"]

    # Felder mit geringem Abstand zum Essen sind gut -> leicht belohnen
    for f in food:
        fx, fy = f["x"], f["y"]
        for y in range(board_height):
            for x in range(board_width):
                if (x, y) == (fx, fy):
                    board_values[y][x] += heuristikValues["FOOD"]  # Direkt auf dem Essen

    # Felder in der Nähe von Köpfen sind gefährlich -> leicht bestrafen
    for snake in opponents:
        if snake["id"] == my_id:
            continue  # die eigene Schlange zählt natürlich nicht
        head = snake["body"][0]

        # Orthogonal angrenzende Felder -10 bestrafen
        orthogonal = [(-1, 0), (1, 0), (0, -1), (0, 1)]
        # Diagonal angrenzende Felder -5 bestrafen
        diagonal = [(-1, -1), (-1, 1), (1, -1), (1, 1)]
        for dx, dy in orthogonal:
            nx, ny = head["x"] + dx, head["y"] + dy
            if 0 <= nx < board_width and 0 <= ny < board_height:
                board_values[ny][nx] += heuristikValues["OPPONENT_ORTHO"]

        for dx, dy in diagonal:
            nx, ny = head["x"] + dx, head["y"] + dy
            if 0 <= nx < board_width and 0 <= ny < board_height:
                board_values[ny][nx] += heuristikValues["OPPONENT_DIA"]

    # Dead-End-Erkennung: Felder mit wenig freien Nachbarn bestrafen
    def count_free_neighbors(x, y):
        directions = [(-1, 0), (1, 0), (0, -1), (0, 1)]
        count = 0
        for dx, dy in directions:
            nx, ny = x + dx, y + dy
            if 0 <= nx < board_width and 0 <= ny < board_height:
                if board_values[ny][nx] > -900:  # nicht blockiert
                    count += 1
        return count

    for y in range(board_height):
        for x in range(board_width):
            if board_values[y][x] <= -100:
                continue  # blockierte Felder ignorieren
            free_neighbors = count_free_neighbors(x, y)
            if free_neighbors <= 1:
                board_values[y][x] += heuristikValues["NEAR_DEATH"]  # Fast sicherer Tod
            elif free_neighbors == 2:
                board_values[y][x] += heuristikValues["LIMITED_MOVEMENT"]  # Eingeschränkt
            elif free_neighbors == 3:
                board_values[y][x] += heuristikValues["RISKY"]  # Leicht riskant
            # bei 4 freie Nachbarn keine Strafe

    return board_values


def get_new_position(head, move):
    if move == "up":
        return {"x": head["x"], "y": head["y"] + 1}
    elif move == "down":
        return {"x": head["x"], "y": head["y"] - 1}
    elif move == "left":
        return {"x": head["x"] - 1, "y": head["y"]}
    elif move == "right":
        return {"x": head["x"] + 1, "y": head["y"]}
    else:  # ungültige Bewegungen ignorieren
        return head


def get_direction(from_node, to_node):
    dx = to_node.x - from_node.x
    dy = to_node.y - from_node.y

    if dx == 1:
        return "right"
    if dx == -1:
        return "left"
    if dy == 1:
        return "up"
    else:
        return "down"  # assuming one has to be right


def print_heuristik_board(heuristik, board_width, board_height, my_head):
    print("\n")  # Leerzeile oben

    for y in range(board_height - 1, -1, -1):
        print()  # Leerzeile zwischen den Zeilen
        row = f"{y:>2} | "  # Y-Achsenbeschriftung (rechtsbündig, zweistellig)
        for x in range(board_width):
            if x == my_head["x"] and y == my_head["y"]:
                row += f"{'HHH':^6}"
            else:
                val = heuristik[y][x]
                row += f"{val:^6}"
        print(row)

    # X-Achsenbeschriftung
    print("   +" + "-" * (board_width * 6))
    x_axis = "     "  # Platzhalter für Y-Achse links
    for x in range(board_width):
        x_axis += f"{x:^6}"
    print(x_axis)
    print()  # Leerzeile unten


def get_blocked_positions(game_state):
    my_length = len(game_state["you"]["body"])
    opponents = game_state['board']['snakes']
    blocked = set()

    # Alle Positionen, die blockiert sind, sind verboten.
    # Da ich selbst in Opponenten enthalten bin, ist mein Körper auch verboten.
    for opponent in opponents:
        for part in opponent["body"]:
            blocked.add((part["x"], part["y"]))

    return blocked


def fallback_safe_direction(game_state,
                            heuristik):  # falls kein sicherer Pfad der Länge simulation_distance gefunden werden kann, wird der beste Einzelzug gewählt
    board_width = game_state['board']['width']
    board_height = game_state['board']['height']
    my_head = game_state["you"]["body"][0]

    def move_value(move):
        new_position = get_new_position(my_head, move)
        nx, ny = new_position["x"], new_position["y"]

        if 0 <= nx < board_width and 0 <= ny < board_height:
            return heuristik[ny][nx]
        else:
            return -10000  # Aus dem Spielfeld heraus zu gehen ist extrem schlecht

    directions = ["up", "down", "left", "right"]
    best_move = max(directions, key=move_value)
    return best_move


def move(game_state):
    my_head = game_state["you"]["body"][0]
    board_width = game_state['board']['width']
    board_height = game_state['board']['height']
    heuristik = get_heuristik(game_state)
    simulation_distance = 5

    def move_value(move):
        new_position = get_new_position(my_head, move)
        nx, ny = new_position["x"], new_position["y"]

        if 0 <= nx < board_width and 0 <= ny < board_height:
            return heuristik[ny][nx]
        else:
            return -10000  # Aus dem Spielfeld heraus zu gehen ist extrem schlecht

    if not silent_mode:
        print("=" * (board_width * 7))
        print_heuristik_board(heuristik, board_width, board_height, my_head)

    x, y = my_head["x"], my_head["y"]

    root = build_tree(heuristik, x, y, None, depth=simulation_distance)
    if not silent_mode:
        print_tree(root)
    best_path, best_weight = find_best_path(root, simulation_distance)
    if not silent_mode:
        print_path(best_path)
    if not best_path or len(best_path) < 2:
        if not silent_mode:
            print("WARNUNG: Kein gültiger Pfad gefunden. Fallback auf sichere Richtung.")
        return {"move": fallback_safe_direction(game_state, heuristik)}  # z.B. random freie Richtung
    best_move = get_direction(root, best_path[1])
    if not silent_mode:
        print("first move of best path: ", best_move)
        print(f"MOVE {game_state['turn']}: Choosing {best_move}")
    return {"move": best_move}


# Start server when `python main.py` is run
if __name__ == "__main__":
    from server import run_server

    run_server({"info": info, "start": start, "move": move, "end": end})


